import type { CardData } from '../types.js'
import axios from 'axios'
import { IS_DEBUG, TELEGRAM_BOT_TOKEN, TELEGRAM_CHANNEL_ID } from '../config.js'
import { sleep } from '../utils.js'

export async function saveToTelegram(
  cards: CardData[],
  title: string,
): Promise<void> {
  if (!TELEGRAM_BOT_TOKEN || !TELEGRAM_CHANNEL_ID) {
    console.error('Telegram bot token / channel ID chưa cấu hình')
    return
  }

  for (const card of cards) {
    let msg = `💳 \\- ${title}\n\n`
    msg += `*Card ${card.link ? `[${card.id}](${card.link})` : card.id}*:\n`
    if (card.vendor) {
      msg += `\\${md(card.vendor)}`
      if (card.lvl) {
        msg += ` ${md(card.lvl)}`
      }
      msg += '\n'
    }
    if (card.bin)
      msg += `\\- BIN: \`${card.bin}\`\n`
    if (card.exp)
      msg += `\\- Exp: \`${card.exp}\`\n`
    if (card.bank) {
      msg += `\\- Bank: \`${md(card.bank)}\`\n`
    }
    if (card.country)
      msg += `\\- Country: ${flagEmoji(card.country)} \\(${card.country}\\)\n`
    if (card.price)
      msg += `\\- Price: ${md(card.price)}\n`

    if (card.seller) {
      const name = md(card.seller.name ?? 'Seller')
      const link = card.seller.link ? md(card.seller.link) : ''
      msg += `\\- Seller: ${link ? `[${name}](${link})` : name} ⭐ ${md(card.seller.rating)} \\(${card.seller.totalReviews} reviews\\)\n`
    }

    if (card.info?.length)
      msg += `\\- Info: ${md(card.info.join(', '))}\n`

    if (card.database)
      msg += `\\- Database: ${md(card.database)}\n`

    msg += '\n'

    await send(msg)
    await sleep(1000)
  }

  if (IS_DEBUG) {
    console.warn(`Đã gửi ${cards.length} card lên kênh ${TELEGRAM_CHANNEL_ID}`)
  }
}

function md(text = ''): string {
  return text.replace(/[_*[\]()~`>#+\-=|{}.!]/g, '\\$&')
}

function flagEmoji(code?: string): string {
  if (!code || code.length !== 2)
    return ''
  const [a, b] = code.toUpperCase()
  return String.fromCodePoint(0x1F1E6 + a.charCodeAt(0) - 65, 0x1F1E6 + b.charCodeAt(0) - 65)
}

async function send(text: string): Promise<void> {
  const url = `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage`
  await axios.post(url, {
    chat_id: TELEGRAM_CHANNEL_ID,
    text,
    parse_mode: 'MarkdownV2',
    disable_web_page_preview: true,
  })
}
