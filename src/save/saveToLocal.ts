import type { CardData } from '../types.js'
import fs from 'node:fs'
import path from 'node:path'
import process from 'node:process'

export function saveToLocal(cards: CardData[], title: string, page: number) {
  const outputFile = path.join(process.cwd(), 'results', `${title}_${page}_${new Date().toISOString()}.json`)
  const outputDir = path.dirname(outputFile)
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true })
  }

  fs.writeFileSync(
    outputFile,
    JSON.stringify(cards, null, 2),
    'utf8',
  )

  console.warn(`Results saved to ${outputFile}`)
  return outputFile
}
