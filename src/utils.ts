import type { CardData, CardSearchParams } from './types.js'
import fs from 'node:fs'
import path from 'node:path'
import { fileURLToPath } from 'node:url'

export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms))
}

export function loadConfig() {
  const __filename = fileURLToPath(import.meta.url)
  const __dirname = path.dirname(__filename)
  const CONFIG_PATH = path.join(__dirname, 'config.json')
  const config = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'))
  return config
}

export function isPassFilter(card: CardData, filter: CardSearchParams) {
  const seller = card.seller

  if (!seller) {
    throw new Error('No seller found')
  }

  if (!seller.rating) {
    throw new Error('No seller rating found')
  }

  if (!seller.totalReviews) {
    throw new Error('No seller total reviews found')
  }

  const sellerRating = seller.rating.split('/')[0]

  if (filter.min_rating && Number(sellerRating) < filter.min_rating) {
    return false
  }
  if (filter.min_reviews && Number(seller.totalReviews) < filter.min_reviews) {
    return false
  }

  return true
}
