import type { ParseResult } from './types.js'

/**
 * Parse HTML content to extract useful information
 * @param html - The HTML content to parse
 * @param baseUrl - The base URL for resolving relative URLs
 * @returns Parsed information from the HTML
 */
export function parseHTML(html: string, baseUrl: string): ParseResult {
  // Simple regex-based parser (for a production app, consider using a proper HTML parser)
  const titleMatch = html.match(/<title>(.*?)<\/title>/i)
  const title = titleMatch ? titleMatch[1].trim() : ''

  const descriptionMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["'](.*?)["'][^>]*>/i)
    || html.match(/<meta[^>]*content=["'](.*?)["'][^>]*name=["']description["'][^>]*>/i)
  const description = descriptionMatch ? descriptionMatch[1].trim() : ''

  // Extract links
  const links: string[] = []
  const linkRegex = /<a[^>]*href=["'](.*?)["'][^>]*>/gi
  let match

  while ((match = linkRegex.exec(html)) !== null) {
    let href = match[1].trim()

    // Skip empty, anchor, javascript and mailto links
    if (!href || href.startsWith('#') || href.startsWith('javascript:') || href.startsWith('mailto:'))
      continue

    // Convert relative URLs to absolute
    if (href.startsWith('/')) {
      href = `${baseUrl}${href}`
    }
    else if (!href.startsWith('http')) {
      href = `${baseUrl}/${href}`
    }

    links.push(href)
  }

  // Extract images
  const images: string[] = []
  const imgRegex = /<img[^>]*src=["'](.*?)["'][^>]*>/gi

  while ((match = imgRegex.exec(html)) !== null) {
    let src = match[1].trim()

    // Convert relative URLs to absolute
    if (src.startsWith('/')) {
      src = `${baseUrl}${src}`
    }
    else if (!src.startsWith('http')) {
      src = `${baseUrl}/${src}`
    }

    images.push(src)
  }

  return {
    title,
    description,
    links: [...new Set(links)], // Remove duplicates
    images: [...new Set(images)], // Remove duplicates
  }
}
