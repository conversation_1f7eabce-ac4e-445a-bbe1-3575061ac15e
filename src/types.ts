/**
 * Result from HTML parsing
 */
export interface ParseResult {
  title: string
  description: string
  links: string[]
  images: string[]
}

/**
 * Final result from crawling
 */
export interface CrawlResult extends ParseResult {
  url: string
  timestamp: string
}

export interface SellerResult {
  id?: string
  name?: string
  link?: string
  rating?: string
  totalReviews?: number
}

export interface CardData {
  id?: string
  link?: string
  db?: string
  bin?: string
  exp?: string
  cvv?: boolean
  type?: string
  vendor?: string
  lvl?: string
  bank?: string
  country?: string
  city?: string
  state?: string
  zip?: string
  info?: string[]
  ref?: boolean
  price?: string
  seller: SellerResult
  notice?: string
  database?: string
}

export interface CardSearchParams {
  price_to?: number // patrick already has this
  price_from?: number // patrick already has this
  min_rating?: number
  min_reviews?: number
  bins?: number[] // patrick already has this
  page?: number
  countries?: string[]
}

export interface Config {
  targetHost: string
  searchParams: CardSearchParams
  cookies: string
}
