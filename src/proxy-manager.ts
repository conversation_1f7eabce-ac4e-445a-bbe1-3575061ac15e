/**
 * Simple proxy manager for rotating proxies when crawling
 */
export class ProxyManager {
  private proxies: string[]
  private currentIndex = 0

  /**
   * Create a new proxy manager with a list of proxies
   * @param proxies List of proxies in format: *****************************:port
   */
  constructor(proxies: string[] = []) {
    this.proxies = proxies
  }

  /**
   * Add a proxy to the rotation list
   */
  addProxy(proxy: string): void {
    this.proxies.push(proxy)
  }

  /**
   * Add multiple proxies to the rotation list
   */
  addProxies(proxies: string[]): void {
    this.proxies.push(...proxies)
  }

  /**
   * Get the next proxy in the rotation
   * @returns The next proxy URL or undefined if no proxies are available
   */
  getNextProxy(): string | undefined {
    if (this.proxies.length === 0) {
      return undefined
    }

    const proxy = this.proxies[this.currentIndex]
    this.currentIndex = (this.currentIndex + 1) % this.proxies.length
    return proxy
  }

  /**
   * Get the current number of available proxies
   */
  get count(): number {
    return this.proxies.length
  }
}
