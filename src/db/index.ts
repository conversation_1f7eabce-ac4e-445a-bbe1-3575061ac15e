import type { CardData } from '../types.js'
import fs from 'node:fs/promises'
import path from 'node:path'
import { fileURLToPath } from 'node:url'
import Database from 'better-sqlite3'
import { IS_DEBUG } from '../config.js'
import { saveToTelegram } from '../save/saveToTelegram.js'

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Database configuration
const DB_DIR = path.join(__dirname, '..', '..', 'db')
const DB_PATH = path.join(DB_DIR, 'cards.db')

// Define table schemas
const PURCHASES_TABLE = `
CREATE TABLE IF NOT EXISTS purchases (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  card_id TEXT NOT NULL,
  link TEXT,
  db TEXT,
  bin TEXT,
  exp TEXT,
  cvv INTEGER,
  type TEXT,
  vendor TEXT,
  lvl TEXT,
  bank TEXT,
  country TEXT,
  city TEXT,
  state TEXT,
  zip TEXT,
  info TEXT,
  ref INTEGER,
  price TEXT,
  seller_id TEXT,
  seller_name TEXT,
  seller_link TEXT,
  seller_rating TEXT,
  seller_total_reviews INTEGER,
  notice TEXT,
  database TEXT,
  checkout_date TEXT DEFAULT (datetime('now')),
  checkout_url TEXT,
  checkout_status INTEGER DEFAULT 1
);

CREATE INDEX IF NOT EXISTS idx_card_id ON purchases(card_id);
CREATE INDEX IF NOT EXISTS idx_card_id_status ON purchases(card_id, checkout_status);
`

/**
 * Initialize the database connection and set up tables
 */
export async function initializeDatabase(): Promise<Database.Database> {
  try {
    // Ensure the database directory exists
    await fs.mkdir(DB_DIR, { recursive: true })

    // Initialize the database connection
    const db = new Database(DB_PATH)

    // Create tables if they don't exist
    db.exec(PURCHASES_TABLE)

    if (IS_DEBUG) {
      console.warn(`Database initialized at ${DB_PATH}`)
    }

    return db
  }
  catch (error) {
    console.error('Error initializing database:', error)
    throw error
  }
}

/**
 * Get singleton database instance
 */
let dbInstance: Database.Database | null = null

export async function getDatabase(): Promise<Database.Database> {
  if (!dbInstance) {
    dbInstance = await initializeDatabase()
  }
  return dbInstance
}

/**
 * Close database connection
 */
export function closeDatabase(): void {
  if (dbInstance) {
    dbInstance.close()
    dbInstance = null
  }
}

/**
 * Insert a purchased card into the database
 */
export async function savePurchasedCard(cardData: CardData): Promise<number> {
  try {
    const db = await getDatabase()

    const stmt = db.prepare(`
      INSERT INTO purchases (
        card_id, link, db, bin, exp, cvv, type, vendor, lvl, bank,
        country, city, state, zip, info, ref, price,
        seller_id, seller_name, seller_link, seller_rating, seller_total_reviews,
        notice, database, checkout_url, checkout_status
      )
      VALUES (
        @card_id, @link, @db, @bin, @exp, @cvv, @type, @vendor, @lvl, @bank,
        @country, @city, @state, @zip, @info, @ref, @price,
        @seller_id, @seller_name, @seller_link, @seller_rating, @seller_total_reviews,
        @notice, @database, @checkout_url, @checkout_status
      )
    `)

    const result = stmt.run({
      card_id: cardData.id || null,
      link: cardData.link || null,
      db: cardData.db || null,
      bin: cardData.bin || null,
      exp: cardData.exp || null,
      cvv: cardData.cvv ? 1 : 0,
      type: cardData.type || null,
      vendor: cardData.vendor || null,
      lvl: cardData.lvl || null,
      bank: cardData.bank || null,
      country: cardData.country || null,
      city: cardData.city || null,
      state: cardData.state || null,
      zip: cardData.zip || null,
      info: cardData.info ? JSON.stringify(cardData.info) : null,
      ref: cardData.ref ? 1 : 0,
      price: cardData.price || null,
      seller_id: cardData.seller?.id || null,
      seller_name: cardData.seller?.name || null,
      seller_link: cardData.seller?.link || null,
      seller_rating: cardData.seller?.rating || null,
      seller_total_reviews: cardData.seller?.totalReviews || null,
      notice: cardData.notice || null,
      database: cardData.database || null,
      checkout_url: cardData.link || null,
      checkout_status: 1, // Default to success
    })

    const insertId = Number(result.lastInsertRowid)
    if (IS_DEBUG) {
      console.warn(`Saved purchased card ${cardData.id} to database (ID: ${insertId})`)
    }

    try {
      await saveToTelegram([cardData], 'Purchased Card')
      if (IS_DEBUG) {
        console.warn(`Telegram notification sent for card ${cardData.id}`)
      }
    }
    catch (telegramError) {
      console.error('Error sending to Telegram:', telegramError)
    }

    return insertId
  }
  catch (error) {
    console.error('Error saving purchased card:', error)
    throw error
  }
}

/**
 * Get a specific card by ID
 */
export async function getSavedCard(cardId: number): Promise<any> {
  try {
    const db = await getDatabase()
    const stmt = db.prepare('SELECT * FROM purchases WHERE id = ?')
    return stmt.get(cardId)
  }
  catch (error) {
    console.error('Error getting saved card:', error)
    return null
  }
}

/**
 * Get all purchased cards
 */
export async function getPurchasedCards(): Promise<any[]> {
  try {
    const db = await getDatabase()
    const stmt = db.prepare('SELECT * FROM purchases ORDER BY checkout_date DESC')
    return stmt.all()
  }
  catch (error) {
    console.error('Error getting purchased cards:', error)
    return []
  }
}

/**
 * Check if a card has already been purchased
 */
export async function isCardPurchased(cardId: string): Promise<boolean> {
  try {
    const db = await getDatabase()
    const stmt = db.prepare('SELECT EXISTS(SELECT 1 FROM purchases WHERE card_id = ? AND checkout_status = 1) as found')
    const result = stmt.get(cardId) as { found: number }
    return result.found === 1
  }
  catch (error) {
    console.error('Error checking if card is purchased:', error)
    return false
  }
}

/**
 * Update the checkout status of a purchased card
 */
export async function updateCheckoutStatus(cardId: string, status: number): Promise<boolean> {
  try {
    const db = await getDatabase()
    const stmt = db.prepare('UPDATE purchases SET checkout_status = ? WHERE card_id = ?')
    const result = stmt.run(status, cardId)
    return result.changes > 0
  }
  catch (error) {
    console.error('Error updating checkout status:', error)
    return false
  }
}
