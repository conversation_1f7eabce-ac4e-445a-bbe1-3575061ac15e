#!/usr/bin/env node

// Message emitter for sending real-time updates to the web interface
// This works by outputting structured JSON messages that the parent process can capture

export class MessageEmitter {
  private static isEnabled = process.env.NODE_ENV !== 'test'

  static info(message: string) {
    this.emit('info', message)
  }

  static warn(message: string) {
    this.emit('warn', message)
  }

  static error(message: string) {
    this.emit('error', message)
  }

  static success(message: string) {
    this.emit('success', message)
  }

  private static emit(type: string, message: string) {
    if (!this.isEnabled)
      return

    // Output to stdout in a format that the parent process can parse
    const data = {
      type,
      message,
      timestamp: new Date().toISOString(),
    }

    // Use a special prefix so the parent process can identify our messages
    console.log(`__CRAWLER_MSG__${JSON.stringify(data)}`)
  }

  // Enable/disable message emission (useful for testing)
  static setEnabled(enabled: boolean) {
    this.isEnabled = enabled
  }
}

// Replace console methods to capture all output
const originalConsoleWarn = console.warn
const originalConsoleError = console.error
const originalConsoleLog = console.log

console.warn = (...args: any[]) => {
  const message = args.join(' ')
  MessageEmitter.warn(message)
  // Also keep the original console output for local debugging
  originalConsoleWarn(...args)
}

console.error = (...args: any[]) => {
  const message = args.join(' ')
  MessageEmitter.error(message)
  originalConsoleError(...args)
}

// Don't override console.log as it might interfere with other outputs
