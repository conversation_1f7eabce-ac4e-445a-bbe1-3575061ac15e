import type { ChildProcess } from 'node:child_process'
import fs from 'node:fs/promises'
import http from 'node:http'
import path from 'node:path'
import process from 'node:process'
import { fileURLToPath } from 'node:url'
import express from 'express'
import { WebSocketServer } from 'ws'

// Get directory name for ES modules
const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// Constants
const CONFIG_PATH = path.join(__dirname, 'config.json')
const PORT = process.env.PORT || 3000

// Initialize express app
const app = express()
const server = http.createServer(app)

// Initialize WebSocket server
const wss = new WebSocketServer({ server, path: '/ws' })

// Store active WebSocket connections
const wsConnections = new Set()

// Crawling state
let crawlingProcess: ChildProcess | null = null
let isCrawling = false

// Message broadcaster for WebSocket clients
class MessageBroadcaster {
  static broadcast(type: string, message: string) {
    const data = JSON.stringify({ type, message })
    wsConnections.forEach((ws: any) => {
      if (ws.readyState === ws.OPEN) {
        ws.send(data)
      }
    })
  }

  static info(message: string) {
    this.broadcast('info', message)
  }

  static warn(message: string) {
    this.broadcast('warn', message)
  }

  static error(message: string) {
    this.broadcast('error', message)
  }

  static success(message: string) {
    this.broadcast('success', message)
  }
}

// WebSocket connection handler
wss.on('connection', (ws) => {
  wsConnections.add(ws)
  console.log('Client connected to WebSocket')

  ws.on('close', () => {
    wsConnections.delete(ws)
    console.log('Client disconnected from WebSocket')
  })

  ws.on('error', (error) => {
    console.error('WebSocket error:', error)
    wsConnections.delete(ws)
  })
})

// Middleware
app.use(express.json())
app.use(express.static(path.join(__dirname, 'public')))

// API Endpoints
app.get('/api/settings', async (req, res) => {
  try {
    const configData = await fs.readFile(CONFIG_PATH, 'utf8')
    const config = JSON.parse(configData)
    res.json(config)
  }
  catch (error) {
    console.error('Error reading config:', error)
    res.status(500).json({ error: 'Failed to read configuration' })
  }
})

app.post('/api/settings', async (req, res) => {
  try {
    const settings = req.body

    // Validate search parameters if they exist
    if (settings.searchParams) {
      const { price_to, min_rating, min_reviews, bins, countries } = settings.searchParams

      // Basic validation
      if ((price_to !== undefined && typeof price_to !== 'number')
        || (min_rating !== undefined && typeof min_rating !== 'number')
        || (min_reviews !== undefined && typeof min_reviews !== 'number')
        || (bins !== undefined && !Array.isArray(bins))
        || (countries !== undefined && !Array.isArray(countries))) {
        return res.status(400).json({
          error: 'Invalid parameters: price_to, min_rating, min_reviews must be numbers, and bins and countries must be arrays',
        })
      }
    }

    // Validate crawler settings
    if (settings.limitPerRun !== undefined && (typeof settings.limitPerRun !== 'number' || settings.limitPerRun < 1)) {
      return res.status(400).json({
        error: 'limitPerRun must be a positive number',
      })
    }

    if (settings.delayPerRun !== undefined && (typeof settings.delayPerRun !== 'number' || settings.delayPerRun < 1)) {
      return res.status(400).json({
        error: 'delayPerRun must be a positive number',
      })
    }

    if (settings.isFakeCheckout !== undefined && typeof settings.isFakeCheckout !== 'boolean') {
      return res.status(400).json({
        error: 'isFakeCheckout must be a boolean',
      })
    }

    // Save to config.json
    await fs.writeFile(CONFIG_PATH, JSON.stringify(settings, null, 2), 'utf8')

    res.json({ success: true, message: 'Settings saved successfully' })
  }
  catch (error) {
    console.error('Error saving config:', error)
    res.status(500).json({ error: 'Failed to save configuration' })
  }
})

// Start crawling endpoint
app.post('/api/start-crawling', async (req, res) => {
  if (isCrawling) {
    return res.status(400).json({ error: 'Crawling is already in progress' })
  }

  try {
    // Check if config exists
    const configData = await fs.readFile(CONFIG_PATH, 'utf8')
    const config = JSON.parse(configData)

    if (!config.targetHost) {
      return res.status(400).json({ error: 'Target host not configured' })
    }

    isCrawling = true
    MessageBroadcaster.info('Starting card crawler...')

    // Import and run the main function from index.ts
    const { spawn } = await import('node:child_process')
    crawlingProcess = spawn('node', [path.join(__dirname, 'index.js')], {
      stdio: ['pipe', 'pipe', 'pipe'],
    })

    // Handle stdout (structured messages and regular output)
    crawlingProcess.stdout?.on('data', (data) => {
      const output = data.toString().trim()
      if (!output)
        return

      // Split by lines to handle multiple messages
      const lines = output.split('\n')
      for (const line of lines) {
        if (line.startsWith('__CRAWLER_MSG__')) {
          // Parse structured message
          try {
            const jsonData = line.substring('__CRAWLER_MSG__'.length)
            const msgData = JSON.parse(jsonData)
            MessageBroadcaster.broadcast(msgData.type, msgData.message)
          }
          catch (error) {
            MessageBroadcaster.error(`Failed to parse crawler message: ${line}`)
          }
        }
        else if (line.trim()) {
          // Regular console output
          MessageBroadcaster.info(line)
        }
      }
    })

    // Handle stderr (error messages)
    crawlingProcess.stderr?.on('data', (data) => {
      const output = data.toString().trim()
      if (!output)
        return

      const lines = output.split('\n')
      for (const line of lines) {
        if (line.startsWith('__CRAWLER_MSG__')) {
          try {
            const jsonData = line.substring('__CRAWLER_MSG__'.length)
            const msgData = JSON.parse(jsonData)
            MessageBroadcaster.broadcast(msgData.type, msgData.message)
          }
          catch (error) {
            MessageBroadcaster.error(`Failed to parse crawler error message: ${line}`)
          }
        }
        else if (line.trim()) {
          MessageBroadcaster.error(line)
        }
      }
    })

    // Handle process exit
    crawlingProcess.on('close', (code) => {
      console.log(`Crawling process closed with code: ${code}`)
      isCrawling = false
      crawlingProcess = null
      if (code === 0) {
        MessageBroadcaster.info('Crawling process completed successfully')
      }
      else if (code === null) {
        MessageBroadcaster.warn('Crawling process was terminated')
      }
      else {
        MessageBroadcaster.error(`Crawling process exited with code ${code}`)
      }
    })

    crawlingProcess.on('error', (error) => {
      console.log(`Crawling process error: ${error.message}`)
      isCrawling = false
      crawlingProcess = null
      MessageBroadcaster.error(`Failed to start crawling process: ${error.message}`)
    })

    crawlingProcess.on('spawn', () => {
      console.log('Crawling process spawned successfully')
      MessageBroadcaster.success('Crawling process started successfully')
    })

    res.json({ success: true, message: 'Crawling started' })
  }
  catch (error) {
    isCrawling = false
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    res.status(500).json({ error: `Failed to start crawling: ${errorMessage}` })
  }
})

// Stop crawling endpoint
app.post('/api/stop-crawling', async (req, res) => {
  if (!isCrawling || !crawlingProcess) {
    return res.status(400).json({ error: 'No crawling process is running' })
  }

  try {
    MessageBroadcaster.warn('Stopping crawling process...')
    crawlingProcess.kill('SIGTERM')

    // Give it a few seconds to gracefully shut down
    setTimeout(() => {
      if (crawlingProcess && !crawlingProcess.killed) {
        crawlingProcess.kill('SIGKILL')
        MessageBroadcaster.error('Crawling process forcefully terminated')
      }
    }, 5000)

    res.json({ success: true, message: 'Stop signal sent' })
  }
  catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    res.status(500).json({ error: `Failed to stop crawling: ${errorMessage}` })
  }
})

// Get crawling status endpoint
app.get('/api/crawling-status', (req, res) => {
  res.json({
    isCrawling,
    hasProcess: !!crawlingProcess,
  })
})

// Start server
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`)
  console.log(`WebSocket server available at ws://localhost:${PORT}/ws`)
})
