import type { AxiosRequestConfig } from 'axios'
import type { ProxyManager } from './proxy-manager.js'
import type { CrawlResult } from './types.js'
import axios from 'axios'
import { IS_DEBUG, IS_FAKE_CHECKOUT } from './config.js'
import { parseHTML } from './parser.js'

/**
 * Extended CrawlResult with raw HTML
 */
export interface ExtendedCrawlResult extends CrawlResult {
  rawHtml: string
}

/**
 * Advanced crawler with anti-detection measures
 */
export class AdvancedCrawler {
  private cookies: Record<string, string> = {}
  private baseUrl: string
  private userAgent: string
  private headers: Record<string, string> = {}
  private referrer?: string
  private proxyManager?: ProxyManager

  constructor(baseUrl: string) {
    this.baseUrl = baseUrl
    this.userAgent = 'Mozilla/5.0 (X11; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0'
  }

  /**
   * Set cookies for authenticated requests
   */
  setCookies(cookieString: string): void {
    // Parse cookie string into object
    const cookies = cookieString.split(';').map(cookie => cookie.trim())
    for (const cookie of cookies) {
      const [name, value] = cookie.split('=')
      if (name && value) {
        this.cookies[name] = value
      }
    }
  }

  /**
   * Set browser-like headers
   */
  setHeaders(headers: Record<string, string>): void {
    this.headers = {
      ...this.headers,
      ...headers,
    }
  }

  /**
   * Set referrer URL
   */
  setReferrer(referrer: string): void {
    this.referrer = referrer
  }

  /**
   * Set proxy manager for IP rotation
   */
  setProxyManager(proxyManager: ProxyManager): void {
    this.proxyManager = proxyManager
  }

  /**
   * Generate random delay to mimic human behavior
   */
  private getRandomDelay(): number {
    // Random delay between 2-5 seconds
    return Math.floor(Math.random() * 3000) + 2000
  }

  /**
   * Helper to format cookies for request
   */
  private formatCookies(): string {
    return Object.entries(this.cookies)
      .map(([name, value]) => `${name}=${value}`)
      .join('; ')
  }

  /**
   * Crawl a specific page with anti-detection measures
   * @param path The page path to crawl
   * @param options Additional crawling options
   */
  async crawlPage(path: string): Promise<ExtendedCrawlResult> {
    try {
      const url = new URL(path, this.baseUrl).toString()
      if (IS_DEBUG) {
        console.warn(`Crawling: ${url}`)
      }

      // Wait random time before request to mimic human behavior
      await new Promise(resolve => setTimeout(resolve, this.getRandomDelay()))

      // Setup request config with all necessary headers
      const config: AxiosRequestConfig = {
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'same-origin',
          'Sec-Fetch-User': '?1',
          'Priority': 'u=0, i',
          'Te': 'trailers',
          ...this.headers,
        },
      }

      // Add cookies if available
      if (Object.keys(this.cookies).length > 0 && config.headers) {
        config.headers.Cookie = this.formatCookies()
      }

      // Add referrer if available
      if (this.referrer && config.headers) {
        config.headers.Referer = this.referrer
      }

      // Add proxy if available
      if (this.proxyManager && this.proxyManager.count > 0) {
        const proxy = this.proxyManager.getNextProxy()
        if (proxy) {
          console.warn(`Using proxy: ${this.maskProxyCredentials(proxy)}`)
          config.proxy = {
            protocol: proxy.split('://')[0],
            host: proxy.split('@').pop()?.split(':')[0] || '',
            port: Number.parseInt(proxy.split(':').pop() || '80', 10),
            auth: this.getProxyAuth(proxy),
          }
        }
      }

      const response = await axios.get(url, config)

      if (response.status !== 200) {
        throw new TypeError(`Failed to fetch data: ${response.status}`)
      }

      // Update cookies from response if any
      const setCookieHeader = response.headers['set-cookie']
      if (setCookieHeader) {
        if (Array.isArray(setCookieHeader)) {
          for (const cookie of setCookieHeader) {
            const [cookiePart] = cookie.split(';')
            const [name, value] = cookiePart.split('=')
            if (name && value) {
              this.cookies[name] = value
            }
          }
        }
      }

      // Store the current URL as referrer for next request
      this.setReferrer(url)

      // Parse the HTML content
      const urlObj = new URL(url)
      const baseUrl = `${urlObj.protocol}//${urlObj.hostname}`
      const result = parseHTML(response.data, baseUrl)

      // Return with raw HTML included
      return {
        url,
        title: result.title,
        description: result.description,
        links: result.links,
        images: result.images,
        timestamp: new Date().toISOString(),
        rawHtml: response.data,
      }
    }
    catch (error) {
      if (error instanceof Error) {
        throw new TypeError(`Crawling error: ${error.message}`)
      }
      throw error
    }
  }

  /**
   * Extract auth from proxy URL
   */
  private getProxyAuth(proxyUrl: string): { username: string, password: string } | undefined {
    const match = proxyUrl.match(/:\/\/([^:]+):([^@]+)@/)
    if (match && match.length === 3) {
      return {
        username: match[1],
        password: match[2],
      }
    }
    return undefined
  }

  /**
   * Mask proxy credentials for logging
   */
  private maskProxyCredentials(proxyUrl: string): string {
    return proxyUrl.replace(/(:\/\/)([^:]+):([^@]+)@/, '$1****:****@')
  }

  /**
   * Get current headers
   */
  getHeaders(): Record<string, string> {
    return { ...this.headers }
  }

  /**
   * Make a POST request to the specified path
   */
  async makePostRequest(path: string, data: string): Promise<{ status: number, data: any }> {
    try {
      const url = new URL(path, this.baseUrl).toString()
      console.warn(`POST request to: ${url}`)

      // Wait random time before request to mimic human behavior
      await new Promise(resolve => setTimeout(resolve, this.getRandomDelay()))

      // Setup request config with all necessary headers
      const config: AxiosRequestConfig = {
        headers: {
          'User-Agent': this.userAgent,
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json, text/javascript, */*; q=0.01',
          ...this.headers,
        },
      }

      // Add cookies if available
      if (Object.keys(this.cookies).length > 0 && config.headers) {
        config.headers.Cookie = this.formatCookies()
      }

      // Add referrer if available
      if (this.referrer && config.headers) {
        config.headers.Referer = this.referrer
      }

      // Add proxy if available
      if (this.proxyManager && this.proxyManager.count > 0) {
        const proxy = this.proxyManager.getNextProxy()
        if (proxy) {
          console.warn(`Using proxy: ${this.maskProxyCredentials(proxy)}`)
          config.proxy = {
            protocol: proxy.split('://')[0],
            host: proxy.split('@').pop()?.split(':')[0] || '',
            port: Number.parseInt(proxy.split(':').pop() || '80', 10),
            auth: this.getProxyAuth(proxy),
          }
        }
      }

      const response = await axios.post(url, data, config)

      // Update cookies from response if any
      const setCookieHeader = response.headers['set-cookie']
      if (setCookieHeader) {
        if (Array.isArray(setCookieHeader)) {
          for (const cookie of setCookieHeader) {
            const [cookiePart] = cookie.split(';')
            const [name, value] = cookiePart.split('=')
            if (name && value) {
              this.cookies[name] = value
            }
          }
        }
      }

      return {
        status: response.status,
        data: response.data,
      }
    }
    catch (error) {
      if (error instanceof Error) {
        throw new TypeError(`POST request error: ${error.message}`)
      }
      throw error
    }
  }

  /**
   * Specialized method for handling checkout requests
   * This handles redirects and captures final destination
   */
  async executeCheckout(path: string = '/checkout'): Promise<{
    status: number
    html: string
    finalUrl: string
    redirectChain: string[]
  }> {
    try {
      const url = new URL(path, this.baseUrl).toString()
      if (IS_DEBUG) {
        console.warn(`Executing checkout: ${url}`)
      }

      if (IS_FAKE_CHECKOUT) {
        return {
          status: 200,
          html: '<html><body><h1>Checkout successful</h1></body></html>',
          finalUrl: 'https://stashpatrick.gl/checkout',
          redirectChain: ['https://stashpatrick.gl/checkout', 'https://stashpatrick.gl/cards/my'],
        }
      }

      // Wait random time before request to mimic human behavior
      await new Promise(resolve => setTimeout(resolve, this.getRandomDelay()))

      // Setup request config for checkout
      const config: AxiosRequestConfig = {
        headers: {
          'User-Agent': this.userAgent,
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'Accept-Language': 'en-US,en;q=0.5',
          'Accept-Encoding': 'gzip, deflate, br',
          'Upgrade-Insecure-Requests': '1',
          'Sec-Fetch-Dest': 'document',
          'Sec-Fetch-Mode': 'navigate',
          'Sec-Fetch-Site': 'same-origin',
          'Sec-Fetch-User': '?1',
          ...this.headers,
        },
        // Don't follow redirects automatically
        maxRedirects: 0,
        // Capture the full response and validation errors
        validateStatus: () => true,
      }

      // Add cookies if available
      if (Object.keys(this.cookies).length > 0 && config.headers) {
        config.headers.Cookie = this.formatCookies()
      }

      // Add referrer if available
      if (this.referrer && config.headers) {
        config.headers.Referer = this.referrer
      }

      // Add proxy if available
      if (this.proxyManager && this.proxyManager.count > 0) {
        const proxy = this.proxyManager.getNextProxy()
        if (proxy) {
          console.warn(`Using proxy: ${this.maskProxyCredentials(proxy)}`)
          config.proxy = {
            protocol: proxy.split('://')[0],
            host: proxy.split('@').pop()?.split(':')[0] || '',
            port: Number.parseInt(proxy.split(':').pop() || '80', 10),
            auth: this.getProxyAuth(proxy),
          }
        }
      }

      // Execute the initial request
      let response = await axios.get(url, config)
      const redirectChain: string[] = [url]
      let finalUrl = url
      let finalHtml = response.data

      // Handle redirects manually (max 5 redirects)
      let redirectCount = 0
      while ((response.status === 301 || response.status === 302 || response.status === 303 || response.status === 307)
        && response.headers.location
        && redirectCount < 5) {
        const redirectUrl = new URL(response.headers.location, finalUrl).toString()
        if (IS_DEBUG) {
          console.warn(`Following redirect (${response.status}) to: ${redirectUrl}`)
        }
        redirectChain.push(redirectUrl)

        // Update referrer to previous URL for each redirect
        if (config.headers) {
          config.headers.Referer = finalUrl
        }

        // Make the redirect request
        response = await axios.get(redirectUrl, config)
        finalUrl = redirectUrl
        finalHtml = response.data
        redirectCount++

        // Update cookies from response if any
        this.processCookiesFromResponse(response)
      }

      // Update the referrer for subsequent requests
      this.setReferrer(finalUrl)

      return {
        status: response.status,
        html: finalHtml,
        finalUrl,
        redirectChain,
      }
    }
    catch (error) {
      if (error instanceof Error) {
        throw new TypeError(`Checkout error: ${error.message}`)
      }
      throw error
    }
  }

  /**
   * Process cookies from a response
   */
  private processCookiesFromResponse(response: any): void {
    const setCookieHeader = response.headers['set-cookie']
    if (setCookieHeader) {
      if (Array.isArray(setCookieHeader)) {
        for (const cookie of setCookieHeader) {
          const [cookiePart] = cookie.split(';')
          const [name, value] = cookiePart.split('=')
          if (name && value) {
            this.cookies[name] = value
          }
        }
      }
    }
  }
}
