# Card Crawler Tool

A specialized web crawler for extracting card data from websites with anti-detection capabilities.

## Features

- **Card Data Extraction**: Parses HTML tables and converts data to JSON
- **Parameter Customization**: Supports all search parameters and filtering options
- **CLI Interface**: Convenient command-line tool for customized searches
- **Anti-Detection Measures**: Bypasses common crawling defenses
- **<PERSON><PERSON> Authentication**: <PERSON>les session cookies for authenticated access
- **Proxy Support**: Rotates between multiple proxies to avoid IP blocking

## Installation

```bash
# Clone the repository
git clone <repository-url>
cd cctool

# Install dependencies
npm install

# Build the project
npm run build
```

## Card Crawler CLI

The CLI tool provides a flexible interface for searching and customizing parameters:

```bash
# Show help message
npm run cli -- --help

# Get all available parameter options
npm run cli -- --get-options

# Save options to a file
npm run cli -- --save-options options.json

# Search with specific parameters
npm run cli -- --vendor VISA --email 1 --page 2

# Save results to a specific file
npm run cli -- --output results/my-search.json
```

### Available Parameters

All parameters from the search endpoint are supported:

- `minidep` - Minimum deposit
- `cardtype` - Card type
- `level` - Card level (CLASSIC, GOLD, PLATINUM, etc.)
- `seller_rep` - Seller reputation
- `vendor` - Card vendor (VISA, MASTERCARD, etc.)
- `rating` - Rating (1-5)
- `valid` - Validity
- `adress` - Has address information (1=yes, 2=no)
- `box` - Has box information
- `ssn` - Has SSN information
- `dob` - Has date of birth information
- `phone` - Has phone information
- `holder` - Has cardholder information
- `email` - Has email information
- `email_part` - Has partial email information
- `ip` - Has IP address information
- `pswrd` - Has password information
- `dl` - Has driver's license information
- `cvv` - Has CVV information
- `page` - Page number

## Telegram Integration

The tool can automatically send card search results to a Telegram chat or channel. To enable this feature:

1. Create a Telegram bot using [@BotFather](https://t.me/botfather)
2. Add the bot to your channel or chat and make it an admin
3. Get your chat ID (use [@userinfobot](https://t.me/userinfobot) or other methods)
4. Set the following environment variables:

```bash
# Enable Telegram integration
export TELEGRAM_ENABLED=true

# Your bot token from BotFather
export TELEGRAM_BOT_TOKEN=123456789:ABCDEFGHIJKLMNOPQRSTUVWXYZ

# Chat ID where to send the results
export TELEGRAM_CHAT_ID=-1001234567890
```

When Telegram integration is enabled, each search result will be sent to your specified chat along with being saved locally.

## Web Interface for Settings

The tool provides a web interface to update and manage search parameters:

```bash
# Start the web interface
npm run server

# Start the web interface in development mode (auto-restart)
npm run dev:server
```

The web interface is accessible at http://localhost:3000 and allows you to:

1. View current search parameters
2. Update price limits, ratings, and reviews thresholds
3. Manage BIN numbers
4. Save settings to config.json

All settings are automatically applied when running searches through the CLI.

## Development

```bash
# Run in development mode (auto-restart on file changes)
npm run dev

# Lint code
npm run lint

# Fix linting issues
npm run lint:fix

# Test different parameter combinations
npm run test-params
```

## Advanced Usage

### Using a Configuration File

You can create a JSON file with all your search parameters:

```json
{
  "vendor": "VISA",
  "level": "PLATINUM",
  "email": "1",
  "page": 3
}
```

Then run the search with:

```bash
npm run cli -- --params my-search.json
```

### Anti-Detection Features

The crawler uses several techniques to bypass crawling defenses:

1. **Browser-like Headers**: Mimics real browser behavior
2. **Random Delays**: Waits between requests to avoid rate limiting
3. **Session Management**: Preserves cookies between requests
4. **Referrer Spoofing**: Simulates proper navigation patterns

## Customizing Proxies

Edit the proxy configuration in `src/index.ts`:

```typescript
const SAMPLE_PROXIES: string[] = [
  'http://username1:<EMAIL>:8080',
  'http://username2:<EMAIL>:8080'
]
```

## License

ISC
