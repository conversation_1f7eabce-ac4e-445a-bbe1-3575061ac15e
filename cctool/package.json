{"name": "cctool", "type": "module", "version": "1.0.0", "description": "Web crawling tool with Node.js", "author": "", "license": "ISC", "keywords": [], "main": "dist/server.js", "bin": {"card-crawler": "dist/cli.js"}, "scripts": {"build": "npm run build:clean && npm run build:bundle && npm run build:assets", "build:clean": "rm -rf dist && mkdir -p dist", "build:bundle": "esbuild src/server.ts --bundle --platform=node --target=node18 --format=esm --outfile=dist/server.js --external:better-sqlite3 --external:ws --external:express --external:node-telegram-bot-api && esbuild src/index.ts --bundle --platform=node --target=node18 --format=esm --outfile=dist/index.js --external:better-sqlite3 --external:node-telegram-bot-api --external:axios --external:cheerio --external:form-data", "build:assets": "mkdir -p dist/public && cp -r src/public/* dist/public/ && cp src/config.json dist/ && tsc src/config.ts --outDir dist --target es2020 --module NodeNext --moduleResolution NodeNext --esModuleInterop --skipLibCheck", "dev": "nodemon", "dev:server": "nodemon --config nodemon.server.json", "watch": "tsc -w", "start": "node dist/index.js", "server": "node dist/server.js", "cli": "node dist/cli.js", "test-params": "node dist/search-params-test.js", "get-options": "node dist/cli.js --get-options", "strategies": "node dist/search-strategies.js", "list-strategies": "node dist/search-strategies.js --list", "run-strategy": "node dist/search-strategies.js --strategy", "run-all-strategies": "node dist/search-strategies.js --all", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^22.15.2", "@types/node-telegram-bot-api": "^0.64.8", "@types/ws": "^8.18.1", "axios": "^1.9.0", "better-sqlite3": "^11.10.0", "cheerio": "^1.0.0-rc.12", "express": "^4.21.2", "node-telegram-bot-api": "^0.66.0", "typescript": "^5.8.3", "ws": "^8.18.3"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@types/express": "^4.17.21", "esbuild": "^0.25.8", "eslint": "^9.25.1", "nodemon": "^3.1.10"}}