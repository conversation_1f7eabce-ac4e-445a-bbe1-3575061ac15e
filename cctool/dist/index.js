#!/usr/bin/env node

// src/index.ts
import process3 from "node:process";

// src/card-crawler.ts
import fs4 from "node:fs/promises";
import path4 from "node:path";

// src/advanced-crawler.ts
import axios from "axios";

// src/utils.ts
import fs from "node:fs";
import path from "node:path";
import { fileURLToPath } from "node:url";
function sleep(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms));
}
function loadConfig() {
  const __filename2 = fileURLToPath(import.meta.url);
  const __dirname2 = path.dirname(__filename2);
  const CONFIG_PATH = path.join(__dirname2, "config.json");
  const config2 = JSON.parse(fs.readFileSync(CONFIG_PATH, "utf8"));
  return config2;
}
function isPassFilter(card, filter) {
  const seller = card.seller;
  if (!seller) {
    throw new Error("No seller found");
  }
  if (!seller.rating) {
    throw new Error("No seller rating found");
  }
  if (!seller.totalReviews) {
    throw new Error("No seller total reviews found");
  }
  const sellerRating = seller.rating.split("/")[0];
  if (filter.min_rating && Number(sellerRating) < filter.min_rating) {
    return false;
  }
  if (filter.min_reviews && Number(seller.totalReviews) < filter.min_reviews) {
    return false;
  }
  return true;
}

// src/config.ts
var config = null;
function getConfig() {
  if (!config) {
    config = loadConfig();
  }
  return config;
}
var TARGET_HOST = getConfig().targetHost || "stashpatrick.gl";
var SAMPLE_PROXIES = getConfig().sampleProxies || [];
var TELEGRAM_BOT_TOKEN = getConfig().telegramBotToken || "";
var TELEGRAM_CHANNEL_ID = getConfig().telegramChannelId || "";
var IS_DEBUG = getConfig().isDebug || false;
var IS_FAKE_CHECKOUT = getConfig().isFakeCheckout !== void 0 ? getConfig().isFakeCheckout : true;
var LIMIT_PER_RUN = getConfig().limitPerRun || 1;
var DELAY_PER_RUN = getConfig().delayPerRun || 5;

// src/parser.ts
function parseHTML(html, baseUrl) {
  const titleMatch = html.match(/<title>(.*?)<\/title>/i);
  const title = titleMatch ? titleMatch[1].trim() : "";
  const descriptionMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["'](.*?)["'][^>]*>/i) || html.match(/<meta[^>]*content=["'](.*?)["'][^>]*name=["']description["'][^>]*>/i);
  const description = descriptionMatch ? descriptionMatch[1].trim() : "";
  const links = [];
  const linkRegex = /<a[^>]*href=["'](.*?)["'][^>]*>/gi;
  let match;
  while ((match = linkRegex.exec(html)) !== null) {
    let href = match[1].trim();
    if (!href || href.startsWith("#") || href.startsWith("javascript:") || href.startsWith("mailto:"))
      continue;
    if (href.startsWith("/")) {
      href = `${baseUrl}${href}`;
    } else if (!href.startsWith("http")) {
      href = `${baseUrl}/${href}`;
    }
    links.push(href);
  }
  const images = [];
  const imgRegex = /<img[^>]*src=["'](.*?)["'][^>]*>/gi;
  while ((match = imgRegex.exec(html)) !== null) {
    let src = match[1].trim();
    if (src.startsWith("/")) {
      src = `${baseUrl}${src}`;
    } else if (!src.startsWith("http")) {
      src = `${baseUrl}/${src}`;
    }
    images.push(src);
  }
  return {
    title,
    description,
    links: [...new Set(links)],
    // Remove duplicates
    images: [...new Set(images)]
    // Remove duplicates
  };
}

// src/advanced-crawler.ts
var AdvancedCrawler = class {
  constructor(baseUrl) {
    this.cookies = {};
    this.headers = {};
    this.baseUrl = baseUrl;
    this.userAgent = "Mozilla/5.0 (X11; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0";
  }
  /**
   * Set cookies for authenticated requests
   */
  setCookies(cookieString) {
    const cookies = cookieString.split(";").map((cookie) => cookie.trim());
    for (const cookie of cookies) {
      const [name, value] = cookie.split("=");
      if (name && value) {
        this.cookies[name] = value;
      }
    }
  }
  /**
   * Set browser-like headers
   */
  setHeaders(headers) {
    this.headers = {
      ...this.headers,
      ...headers
    };
  }
  /**
   * Set referrer URL
   */
  setReferrer(referrer) {
    this.referrer = referrer;
  }
  /**
   * Set proxy manager for IP rotation
   */
  setProxyManager(proxyManager) {
    this.proxyManager = proxyManager;
  }
  /**
   * Generate random delay to mimic human behavior
   */
  getRandomDelay() {
    return Math.floor(Math.random() * 3e3) + 2e3;
  }
  /**
   * Helper to format cookies for request
   */
  formatCookies() {
    return Object.entries(this.cookies).map(([name, value]) => `${name}=${value}`).join("; ");
  }
  /**
   * Crawl a specific page with anti-detection measures
   * @param path The page path to crawl
   * @param options Additional crawling options
   */
  async crawlPage(path5) {
    try {
      const url = new URL(path5, this.baseUrl).toString();
      if (IS_DEBUG) {
        console.warn(`Crawling: ${url}`);
      }
      await new Promise((resolve) => setTimeout(resolve, this.getRandomDelay()));
      const config2 = {
        headers: {
          "User-Agent": this.userAgent,
          "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
          "Accept-Language": "en-US,en;q=0.5",
          "Accept-Encoding": "gzip, deflate, br",
          "Upgrade-Insecure-Requests": "1",
          "Sec-Fetch-Dest": "document",
          "Sec-Fetch-Mode": "navigate",
          "Sec-Fetch-Site": "same-origin",
          "Sec-Fetch-User": "?1",
          "Priority": "u=0, i",
          "Te": "trailers",
          ...this.headers
        }
      };
      if (Object.keys(this.cookies).length > 0 && config2.headers) {
        config2.headers.Cookie = this.formatCookies();
      }
      if (this.referrer && config2.headers) {
        config2.headers.Referer = this.referrer;
      }
      if (this.proxyManager && this.proxyManager.count > 0) {
        const proxy = this.proxyManager.getNextProxy();
        if (proxy) {
          console.warn(`Using proxy: ${this.maskProxyCredentials(proxy)}`);
          config2.proxy = {
            protocol: proxy.split("://")[0],
            host: proxy.split("@").pop()?.split(":")[0] || "",
            port: Number.parseInt(proxy.split(":").pop() || "80", 10),
            auth: this.getProxyAuth(proxy)
          };
        }
      }
      const response = await axios.get(url, config2);
      if (response.status !== 200) {
        throw new TypeError(`Failed to fetch data: ${response.status}`);
      }
      const setCookieHeader = response.headers["set-cookie"];
      if (setCookieHeader) {
        if (Array.isArray(setCookieHeader)) {
          for (const cookie of setCookieHeader) {
            const [cookiePart] = cookie.split(";");
            const [name, value] = cookiePart.split("=");
            if (name && value) {
              this.cookies[name] = value;
            }
          }
        }
      }
      this.setReferrer(url);
      const urlObj = new URL(url);
      const baseUrl = `${urlObj.protocol}//${urlObj.hostname}`;
      const result = parseHTML(response.data, baseUrl);
      return {
        url,
        title: result.title,
        description: result.description,
        links: result.links,
        images: result.images,
        timestamp: (/* @__PURE__ */ new Date()).toISOString(),
        rawHtml: response.data
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new TypeError(`Crawling error: ${error.message}`);
      }
      throw error;
    }
  }
  /**
   * Extract auth from proxy URL
   */
  getProxyAuth(proxyUrl) {
    const match = proxyUrl.match(/:\/\/([^:]+):([^@]+)@/);
    if (match && match.length === 3) {
      return {
        username: match[1],
        password: match[2]
      };
    }
    return void 0;
  }
  /**
   * Mask proxy credentials for logging
   */
  maskProxyCredentials(proxyUrl) {
    return proxyUrl.replace(/(:\/\/)([^:]+):([^@]+)@/, "$1****:****@");
  }
  /**
   * Get current headers
   */
  getHeaders() {
    return { ...this.headers };
  }
  /**
   * Make a POST request to the specified path
   */
  async makePostRequest(path5, data) {
    try {
      const url = new URL(path5, this.baseUrl).toString();
      console.warn(`POST request to: ${url}`);
      await new Promise((resolve) => setTimeout(resolve, this.getRandomDelay()));
      const config2 = {
        headers: {
          "User-Agent": this.userAgent,
          "Content-Type": "application/x-www-form-urlencoded",
          "Accept": "application/json, text/javascript, */*; q=0.01",
          ...this.headers
        }
      };
      if (Object.keys(this.cookies).length > 0 && config2.headers) {
        config2.headers.Cookie = this.formatCookies();
      }
      if (this.referrer && config2.headers) {
        config2.headers.Referer = this.referrer;
      }
      if (this.proxyManager && this.proxyManager.count > 0) {
        const proxy = this.proxyManager.getNextProxy();
        if (proxy) {
          console.warn(`Using proxy: ${this.maskProxyCredentials(proxy)}`);
          config2.proxy = {
            protocol: proxy.split("://")[0],
            host: proxy.split("@").pop()?.split(":")[0] || "",
            port: Number.parseInt(proxy.split(":").pop() || "80", 10),
            auth: this.getProxyAuth(proxy)
          };
        }
      }
      const response = await axios.post(url, data, config2);
      const setCookieHeader = response.headers["set-cookie"];
      if (setCookieHeader) {
        if (Array.isArray(setCookieHeader)) {
          for (const cookie of setCookieHeader) {
            const [cookiePart] = cookie.split(";");
            const [name, value] = cookiePart.split("=");
            if (name && value) {
              this.cookies[name] = value;
            }
          }
        }
      }
      return {
        status: response.status,
        data: response.data
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new TypeError(`POST request error: ${error.message}`);
      }
      throw error;
    }
  }
  /**
   * Specialized method for handling checkout requests
   * This handles redirects and captures final destination
   */
  async executeCheckout(path5 = "/checkout") {
    try {
      const url = new URL(path5, this.baseUrl).toString();
      if (IS_DEBUG) {
        console.warn(`Executing checkout: ${url}`);
      }
      if (IS_FAKE_CHECKOUT) {
        return {
          status: 200,
          html: "<html><body><h1>Checkout successful</h1></body></html>",
          finalUrl: "https://stashpatrick.gl/checkout",
          redirectChain: ["https://stashpatrick.gl/checkout", "https://stashpatrick.gl/cards/my"]
        };
      }
      await new Promise((resolve) => setTimeout(resolve, this.getRandomDelay()));
      const config2 = {
        headers: {
          "User-Agent": this.userAgent,
          "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
          "Accept-Language": "en-US,en;q=0.5",
          "Accept-Encoding": "gzip, deflate, br",
          "Upgrade-Insecure-Requests": "1",
          "Sec-Fetch-Dest": "document",
          "Sec-Fetch-Mode": "navigate",
          "Sec-Fetch-Site": "same-origin",
          "Sec-Fetch-User": "?1",
          ...this.headers
        },
        // Don't follow redirects automatically
        maxRedirects: 0,
        // Capture the full response and validation errors
        validateStatus: () => true
      };
      if (Object.keys(this.cookies).length > 0 && config2.headers) {
        config2.headers.Cookie = this.formatCookies();
      }
      if (this.referrer && config2.headers) {
        config2.headers.Referer = this.referrer;
      }
      if (this.proxyManager && this.proxyManager.count > 0) {
        const proxy = this.proxyManager.getNextProxy();
        if (proxy) {
          console.warn(`Using proxy: ${this.maskProxyCredentials(proxy)}`);
          config2.proxy = {
            protocol: proxy.split("://")[0],
            host: proxy.split("@").pop()?.split(":")[0] || "",
            port: Number.parseInt(proxy.split(":").pop() || "80", 10),
            auth: this.getProxyAuth(proxy)
          };
        }
      }
      let response = await axios.get(url, config2);
      const redirectChain = [url];
      let finalUrl = url;
      let finalHtml = response.data;
      let redirectCount = 0;
      while ((response.status === 301 || response.status === 302 || response.status === 303 || response.status === 307) && response.headers.location && redirectCount < 5) {
        const redirectUrl = new URL(response.headers.location, finalUrl).toString();
        if (IS_DEBUG) {
          console.warn(`Following redirect (${response.status}) to: ${redirectUrl}`);
        }
        redirectChain.push(redirectUrl);
        if (config2.headers) {
          config2.headers.Referer = finalUrl;
        }
        response = await axios.get(redirectUrl, config2);
        finalUrl = redirectUrl;
        finalHtml = response.data;
        redirectCount++;
        this.processCookiesFromResponse(response);
      }
      this.setReferrer(finalUrl);
      return {
        status: response.status,
        html: finalHtml,
        finalUrl,
        redirectChain
      };
    } catch (error) {
      if (error instanceof Error) {
        throw new TypeError(`Checkout error: ${error.message}`);
      }
      throw error;
    }
  }
  /**
   * Process cookies from a response
   */
  processCookiesFromResponse(response) {
    const setCookieHeader = response.headers["set-cookie"];
    if (setCookieHeader) {
      if (Array.isArray(setCookieHeader)) {
        for (const cookie of setCookieHeader) {
          const [cookiePart] = cookie.split(";");
          const [name, value] = cookiePart.split("=");
          if (name && value) {
            this.cookies[name] = value;
          }
        }
      }
    }
  }
};

// src/card-parser.ts
import * as cheerio from "cheerio";
function parseCardTable(html, url) {
  const $ = cheerio.load(html);
  const cards = [];
  const tableExists = $(".table-responsive table").length > 0;
  if (!tableExists) {
    console.warn("No cards found!");
    return [];
  }
  const table = $(".table-responsive table");
  const headers = [];
  table.find("thead th").each((index, th) => {
    let header = $(th).text().trim().toLowerCase().replace(/\s+/g, "_");
    if (header === "" && $(th).find('input[type="checkbox"]').length) {
      header = "cardbuy";
    }
    headers.push(header);
  });
  if (IS_DEBUG) {
    console.warn(`Found headers: ${headers.join(", ")}`);
  }
  const tableRows = table.find("tbody > tr");
  if (IS_DEBUG) {
    console.warn(`Found ${tableRows.length} rows`);
  }
  tableRows.each((rowIndex, row) => {
    try {
      const card = {
        seller: {},
        info: []
      };
      $(row).find("th, td").each((cellIndex, cell) => {
        const header = headers[cellIndex];
        if (!header)
          return;
        let dbLink, notice, reviewText, reviewMatch;
        let infoHtml, infoItems, countryText, checkbox;
        switch (header) {
          case "db": {
            dbLink = $(cell).find('a[href*="/cards/search"]').first();
            if (dbLink.length) {
              card.database = dbLink.text().trim().replace(/\s+/g, " ");
            }
            notice = $(cell).find(".text-danger").first();
            if (notice.length) {
              card.notice = notice.text().trim();
            }
            const sellerInfo = $(cell).find('a[href*="/seller/"]').first();
            const link = sellerInfo.attr("href");
            if (sellerInfo) {
              card.seller = {
                id: extractSellerId(link || ""),
                name: sellerInfo.find("b").text().trim(),
                link,
                rating: $(cell).find(".text-warning + div").text().trim(),
                totalReviews: 0
              };
              if (card.seller.id) {
                card.link = buildCardLink(card.seller.id, url);
              }
              reviewText = $(cell).find('span:contains("Total review:")').text();
              reviewMatch = reviewText.match(/Total review: (\d+)/);
              if (reviewMatch && reviewMatch[1]) {
                card.seller.totalReviews = Number.parseInt(reviewMatch[1], 10);
              }
            }
            break;
          }
          case "bin":
            card.bin = $(cell).text().trim();
            break;
          case "exp":
            card.exp = $(cell).text().trim();
            break;
          case "cvv":
            card.cvv = $(cell).find(".text-success").length > 0;
            break;
          case "type":
            card.type = $(cell).text().trim();
            break;
          case "vendor":
            card.vendor = $(cell).text().trim();
            break;
          case "lvl":
            card.lvl = $(cell).text().trim();
            break;
          case "bank":
            card.bank = $(cell).text().trim();
            break;
          case "country":
            countryText = $(cell).text().trim();
            card.country = countryText.replace(/\s+/g, "");
            break;
          case "city":
            card.city = $(cell).text().trim();
            break;
          case "state":
            card.state = $(cell).text().trim();
            break;
          case "zip":
            card.zip = $(cell).text().trim();
            break;
          case "info":
            infoHtml = $(cell).html();
            if (infoHtml) {
              infoItems = infoHtml.split("<br>");
              card.info = infoItems.map((item) => $(item).text().trim()).filter(Boolean);
            }
            break;
          case "ref":
            card.ref = $(cell).find(".text-success").length > 0;
            break;
          case "price":
            card.price = $(cell).text().trim();
            break;
          case "cardbuy":
            checkbox = $(cell).find("input.cardbuy");
            if (checkbox.length) {
              card.id = checkbox.val();
            }
            break;
        }
      });
      expectedSellerData(card.seller);
      expectedCardData(card);
      cards.push(card);
    } catch (error) {
      console.error(`Error parsing row ${rowIndex}:`, error);
    }
  });
  return cards;
}
function parsePagination(html) {
  const $ = cheerio.load(html);
  let currentPage = 1;
  let totalPages = 1;
  const pagination = $(".pagination");
  if (pagination.length) {
    const activePage = pagination.find("li.active");
    if (activePage.length) {
      currentPage = Number.parseInt(activePage.text().trim(), 10) || 1;
      console.warn(`Current page: ${currentPage}`);
    }
    const pageLinks = pagination.find("li a");
    if (pageLinks.length) {
      const pageNumbers = [];
      pageLinks.each((_, link) => {
        const pageText = $(link).text().trim();
        const pageNum = Number.parseInt(pageText, 10);
        if (!Number.isNaN(pageNum)) {
          pageNumbers.push(pageNum);
        }
      });
      if (pageNumbers.length > 0) {
        totalPages = Math.max(...pageNumbers);
      }
      console.warn(`Total pages: ${totalPages}`);
    }
  } else {
    if (IS_DEBUG) {
      console.warn("No pagination found");
    }
  }
  return { currentPage, totalPages };
}
function expectedSellerData(seller) {
  if (!seller.name || !seller.link || !seller.rating || !seller.totalReviews) {
    throw new Error("Seller data is missing required fields, Please update the parser");
  }
}
function expectedCardData(card) {
  if (!card.id) {
    throw new Error("Card data is missing required fields, Please update the parser");
  }
}
function extractSellerId(sellerLink) {
  const url = new URL(sellerLink);
  return url.pathname.split("/").pop() || "";
}
function buildCardLink(sellerId, searchPath) {
  const url = new URL(searchPath);
  url.searchParams.delete("page");
  url.searchParams.append("seller[]", sellerId);
  return url.toString();
}

// src/db/index.ts
import fs2 from "node:fs/promises";
import path2 from "node:path";
import { fileURLToPath as fileURLToPath2 } from "node:url";
import Database from "better-sqlite3";

// src/save/saveToTelegram.ts
import axios2 from "axios";
async function saveToTelegram(cards, title) {
  if (!TELEGRAM_BOT_TOKEN || !TELEGRAM_CHANNEL_ID) {
    console.error("Telegram bot token / channel ID ch\u01B0a c\u1EA5u h\xECnh");
    return;
  }
  for (const card of cards) {
    let msg = `\u{1F4B3} \\- ${title}

`;
    msg += `*Card ${card.link ? `[${card.id}](${card.link})` : card.id}*:
`;
    if (card.vendor) {
      msg += `\\${md(card.vendor)}`;
      if (card.lvl) {
        msg += ` ${md(card.lvl)}`;
      }
      msg += "\n";
    }
    if (card.bin)
      msg += `\\- BIN: \`${card.bin}\`
`;
    if (card.exp)
      msg += `\\- Exp: \`${card.exp}\`
`;
    if (card.bank) {
      msg += `\\- Bank: \`${md(card.bank)}\`
`;
    }
    if (card.country)
      msg += `\\- Country: ${flagEmoji(card.country)} \\(${card.country}\\)
`;
    if (card.price)
      msg += `\\- Price: ${md(card.price)}
`;
    if (card.seller) {
      const name = md(card.seller.name ?? "Seller");
      const link = card.seller.link ? md(card.seller.link) : "";
      msg += `\\- Seller: ${link ? `[${name}](${link})` : name} \u2B50 ${md(card.seller.rating)} \\(${card.seller.totalReviews} reviews\\)
`;
    }
    if (card.info?.length)
      msg += `\\- Info: ${md(card.info.join(", "))}
`;
    if (card.database)
      msg += `\\- Database: ${md(card.database)}
`;
    msg += "\n";
    await send(msg);
    await sleep(1e3);
  }
  if (IS_DEBUG) {
    console.warn(`\u0110\xE3 g\u1EEDi ${cards.length} card l\xEAn k\xEAnh ${TELEGRAM_CHANNEL_ID}`);
  }
}
function md(text = "") {
  return text.replace(/[_*[\]()~`>#+\-=|{}.!]/g, "\\$&");
}
function flagEmoji(code) {
  if (!code || code.length !== 2)
    return "";
  const [a, b] = code.toUpperCase();
  return String.fromCodePoint(127462 + a.charCodeAt(0) - 65, 127462 + b.charCodeAt(0) - 65);
}
async function send(text) {
  const url = `https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/sendMessage`;
  await axios2.post(url, {
    chat_id: TELEGRAM_CHANNEL_ID,
    text,
    parse_mode: "MarkdownV2",
    disable_web_page_preview: true
  });
}

// src/db/index.ts
var __filename = fileURLToPath2(import.meta.url);
var __dirname = path2.dirname(__filename);
var DB_DIR = path2.join(__dirname, "..", "..", "db");
var DB_PATH = path2.join(DB_DIR, "cards.db");
var PURCHASES_TABLE = `
CREATE TABLE IF NOT EXISTS purchases (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  card_id TEXT NOT NULL,
  link TEXT,
  db TEXT,
  bin TEXT,
  exp TEXT,
  cvv INTEGER,
  type TEXT,
  vendor TEXT,
  lvl TEXT,
  bank TEXT,
  country TEXT,
  city TEXT,
  state TEXT,
  zip TEXT,
  info TEXT,
  ref INTEGER,
  price TEXT,
  seller_id TEXT,
  seller_name TEXT,
  seller_link TEXT,
  seller_rating TEXT,
  seller_total_reviews INTEGER,
  notice TEXT,
  database TEXT,
  checkout_date TEXT DEFAULT (datetime('now')),
  checkout_url TEXT,
  checkout_status INTEGER DEFAULT 1
);

CREATE INDEX IF NOT EXISTS idx_card_id ON purchases(card_id);
CREATE INDEX IF NOT EXISTS idx_card_id_status ON purchases(card_id, checkout_status);
`;
async function initializeDatabase() {
  try {
    await fs2.mkdir(DB_DIR, { recursive: true });
    const db = new Database(DB_PATH);
    db.exec(PURCHASES_TABLE);
    if (IS_DEBUG) {
      console.warn(`Database initialized at ${DB_PATH}`);
    }
    return db;
  } catch (error) {
    console.error("Error initializing database:", error);
    throw error;
  }
}
var dbInstance = null;
async function getDatabase() {
  if (!dbInstance) {
    dbInstance = await initializeDatabase();
  }
  return dbInstance;
}
function closeDatabase() {
  if (dbInstance) {
    dbInstance.close();
    dbInstance = null;
  }
}
async function savePurchasedCard(cardData) {
  try {
    const db = await getDatabase();
    const stmt = db.prepare(`
      INSERT INTO purchases (
        card_id, link, db, bin, exp, cvv, type, vendor, lvl, bank,
        country, city, state, zip, info, ref, price,
        seller_id, seller_name, seller_link, seller_rating, seller_total_reviews,
        notice, database, checkout_url, checkout_status
      )
      VALUES (
        @card_id, @link, @db, @bin, @exp, @cvv, @type, @vendor, @lvl, @bank,
        @country, @city, @state, @zip, @info, @ref, @price,
        @seller_id, @seller_name, @seller_link, @seller_rating, @seller_total_reviews,
        @notice, @database, @checkout_url, @checkout_status
      )
    `);
    const result = stmt.run({
      card_id: cardData.id || null,
      link: cardData.link || null,
      db: cardData.db || null,
      bin: cardData.bin || null,
      exp: cardData.exp || null,
      cvv: cardData.cvv ? 1 : 0,
      type: cardData.type || null,
      vendor: cardData.vendor || null,
      lvl: cardData.lvl || null,
      bank: cardData.bank || null,
      country: cardData.country || null,
      city: cardData.city || null,
      state: cardData.state || null,
      zip: cardData.zip || null,
      info: cardData.info ? JSON.stringify(cardData.info) : null,
      ref: cardData.ref ? 1 : 0,
      price: cardData.price || null,
      seller_id: cardData.seller?.id || null,
      seller_name: cardData.seller?.name || null,
      seller_link: cardData.seller?.link || null,
      seller_rating: cardData.seller?.rating || null,
      seller_total_reviews: cardData.seller?.totalReviews || null,
      notice: cardData.notice || null,
      database: cardData.database || null,
      checkout_url: cardData.link || null,
      checkout_status: 1
      // Default to success
    });
    const insertId = Number(result.lastInsertRowid);
    if (IS_DEBUG) {
      console.warn(`Saved purchased card ${cardData.id} to database (ID: ${insertId})`);
    }
    try {
      await saveToTelegram([cardData], "Purchased Card");
      if (IS_DEBUG) {
        console.warn(`Telegram notification sent for card ${cardData.id}`);
      }
    } catch (telegramError) {
      console.error("Error sending to Telegram:", telegramError);
    }
    return insertId;
  } catch (error) {
    console.error("Error saving purchased card:", error);
    throw error;
  }
}
async function isCardPurchased(cardId) {
  try {
    const db = await getDatabase();
    const stmt = db.prepare("SELECT EXISTS(SELECT 1 FROM purchases WHERE card_id = ? AND checkout_status = 1) as found");
    const result = stmt.get(cardId);
    return result.found === 1;
  } catch (error) {
    console.error("Error checking if card is purchased:", error);
    return false;
  }
}

// src/save/saveToLocal.ts
import fs3 from "node:fs";
import path3 from "node:path";
import process2 from "node:process";
function saveToLocal(cards, title, page) {
  const outputFile = path3.join(process2.cwd(), "results", `${title}_${page}_${(/* @__PURE__ */ new Date()).toISOString()}.json`);
  const outputDir = path3.dirname(outputFile);
  if (!fs3.existsSync(outputDir)) {
    fs3.mkdirSync(outputDir, { recursive: true });
  }
  fs3.writeFileSync(
    outputFile,
    JSON.stringify(cards, null, 2),
    "utf8"
  );
  console.warn(`Results saved to ${outputFile}`);
  return outputFile;
}

// src/save/index.ts
function saveCardData(cards, title, page) {
  const localFile = saveToLocal(cards, title, page);
  return localFile;
}

// src/card-crawler.ts
var DEFAULT_PARAMS = {
  minidep: "ALL",
  cardtype: "ALL",
  level: "ALL",
  seller_rep: "ALL",
  vendor: "ALL",
  rating: "ALL",
  valid: "ALL",
  adress: "ALL",
  box: "ALL",
  ssn: "ALL",
  dob: "ALL",
  phone: "ALL",
  holder: "ALL",
  email: "ALL",
  email_part: "ALL",
  ip: "ALL",
  pswrd: "ALL",
  dl: "ALL",
  cvv: "ALL",
  page: 1,
  price_to: void 0,
  country: "ALL"
};
var DEFAULT_USER_AGENT = "Mozilla/5.0 (X11; Linux x86_64; rv:137.0) Gecko/20100101 Firefox/137.0";
var CardCrawler = class {
  /**
   * Create a new Card Crawler
   */
  constructor(host, cookies, proxyManager, options = {}) {
    this.currentPage = 1;
    this.totalPages = 1;
    this.csrfToken = "";
    this.baseHost = host;
    this.title = options.title || "cards";
    this.debugDir = options.debugDir || "debug";
    this.userAgent = options.userAgent || DEFAULT_USER_AGENT;
    this.crawler = new AdvancedCrawler(`https://${host}`);
    this.crawler.setCookies(cookies);
    this.crawler.setReferrer(`https://${host}/cards/search`);
    this.crawler.setHeaders({
      "User-Agent": this.userAgent,
      "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
      "Accept-Language": "en-US,en;q=0.5",
      "Accept-Encoding": "gzip, deflate, br",
      "Upgrade-Insecure-Requests": "1",
      "Sec-Fetch-Dest": "document",
      "Sec-Fetch-Mode": "navigate",
      "Sec-Fetch-Site": "same-origin",
      "Sec-Fetch-User": "?1"
    });
    if (proxyManager) {
      this.crawler.setProxyManager(proxyManager);
    }
  }
  /**
   * Build a URL with the provided search parameters
   */
  buildSearchUrl(params) {
    const mergedParams = { ...DEFAULT_PARAMS, ...params };
    const acceptedParams = Object.keys(DEFAULT_PARAMS);
    const queryParams = new URLSearchParams();
    for (const [key, value] of Object.entries(mergedParams)) {
      if (key === "country" && params.countries) {
        continue;
      }
      if (acceptedParams.includes(key) && value !== void 0) {
        queryParams.append(key, value.toString());
      }
    }
    if (params.bins) {
      queryParams.append("bins", params.bins.join(","));
    }
    if (params.countries && params.countries.length > 0) {
      params.countries.forEach((country) => {
        queryParams.append("country[]", country);
      });
    }
    return `/cards/search?${queryParams.toString()}`;
  }
  /**
   * Save HTML response to a file for debugging
   */
  async saveDebugHtml(html, url, prefix = "response") {
    if (!IS_DEBUG)
      return "";
    try {
      await fs4.mkdir(this.debugDir, { recursive: true });
      const timestamp = (/* @__PURE__ */ new Date()).toISOString().replace(/[:.]/g, "-");
      const sanitizedUrl = url.replace(/[^a-z0-9]/gi, "_").substring(0, 50);
      const filename = `${prefix}_${timestamp}_${sanitizedUrl}.html`;
      const filePath = path4.join(this.debugDir, filename);
      await fs4.writeFile(filePath, html, "utf8");
      if (IS_DEBUG) {
        console.warn(`Debug HTML saved to: ${filePath}`);
      }
      return filePath;
    } catch (error) {
      console.error("Error saving debug HTML:", error);
      return "";
    }
  }
  /**
   * Extract CSRF Token from HTML response
   */
  extractCsrfToken(html) {
    try {
      const metaTagMatch = html.match(/<meta name="csrf-token" content="([^"]+)"/i);
      if (metaTagMatch && metaTagMatch[1]) {
        return metaTagMatch[1];
      }
      const inputMatch = html.match(/<input type="hidden" name="_token" value="([^"]+)"/i);
      if (inputMatch && inputMatch[1]) {
        return inputMatch[1];
      }
      const jsMatch = html.match(/var\s+csrfToken\s*=\s*['"]([^'"]+)['"]/i);
      if (jsMatch && jsMatch[1]) {
        return jsMatch[1];
      }
      console.warn("Could not extract CSRF token from HTML");
      return "";
    } catch (error) {
      console.error("Error extracting CSRF token:", error);
      return "";
    }
  }
  /**
   * Update crawler configuration
   */
  updateConfig(options) {
    if (options.userAgent) {
      this.userAgent = options.userAgent;
      this.crawler.setHeaders({
        "User-Agent": options.userAgent,
        "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Accept-Encoding": "gzip, deflate, br",
        "Upgrade-Insecure-Requests": "1",
        "Sec-Fetch-Dest": "document",
        "Sec-Fetch-Mode": "navigate",
        "Sec-Fetch-Site": "same-origin",
        "Sec-Fetch-User": "?1"
      });
    }
    if (options.cookies) {
      this.crawler.setCookies(options.cookies);
    }
  }
  /**
   * Crawl the cards/search endpoint with the provided parameters
   */
  async searchCards(params = {}) {
    try {
      const searchPath = this.buildSearchUrl(params);
      const response = await this.crawler.crawlPage(searchPath);
      const debugFile = await this.saveDebugHtml(response.rawHtml, response.url);
      this.csrfToken = this.extractCsrfToken(response.rawHtml);
      if (this.csrfToken && IS_DEBUG) {
        console.warn(`CSRF Token extracted: ${this.csrfToken.substring(0, 10)}...`);
      }
      const authCheck = this.checkForAuthenticationIssues(response.rawHtml);
      if (!authCheck.isAuthenticated) {
        console.warn(`Authentication issue detected: ${authCheck.reason}`);
        return {
          cards: [],
          pagination: {
            currentPage: 0,
            totalPages: 0
          },
          authenticated: false,
          reason: authCheck.reason,
          debugFile,
          csrfToken: this.csrfToken
        };
      }
      const rawCards = parseCardTable(response.rawHtml, response.url).filter((card) => card.id);
      console.warn(`Founds ${rawCards.length} cards in the search`);
      const cards = [];
      for (const card of rawCards) {
        if (await isCardPurchased(card.id)) {
          console.warn(`Card ${card.id} already purchased`);
          continue;
        }
        if (!isPassFilter(card, params)) {
          continue;
        }
        cards.push(card);
      }
      console.warn(`Founds ${cards.length} cards to purchase`);
      const pagination = parsePagination(response.rawHtml);
      this.currentPage = pagination.currentPage;
      this.totalPages = pagination.totalPages;
      return {
        cards,
        pagination: {
          currentPage: this.currentPage,
          totalPages: this.totalPages
        },
        authenticated: true,
        debugFile,
        csrfToken: this.csrfToken
      };
    } catch (error) {
      console.error("Error during card search:", error);
      throw error;
    }
  }
  /**
   * Get CSRF token
   */
  getCsrfToken() {
    return this.csrfToken;
  }
  /**
   * Add a card to the cart
   * @param cardId The ID of the card to add to the cart
   * @returns Response data from the server
   */
  async addToCart(cardIds) {
    try {
      if (!this.csrfToken) {
        console.warn("No CSRF token available, attempting to fetch one first");
        const searchResult = await this.searchCards({ page: 1 });
        if (!searchResult.authenticated) {
          return {
            success: false,
            message: `Failed to authenticate: ${searchResult.reason}`,
            debugFile: searchResult.debugFile
          };
        }
      }
      const formData = new URLSearchParams();
      formData.append("_token", this.csrfToken);
      formData.append("cards[]", cardIds.join(","));
      const originalHeaders = this.crawler.getHeaders();
      this.crawler.setHeaders({
        ...originalHeaders,
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "X-Requested-With": "XMLHttpRequest",
        "Origin": `https://${this.baseHost}`,
        "Referer": `https://${this.baseHost}/cards/buy`
      });
      const response = await this.crawler.makePostRequest("/addtocart", formData.toString());
      this.crawler.setHeaders(originalHeaders);
      const debugFile = await this.saveDebugHtml(
        typeof response.data === "string" ? response.data : JSON.stringify(response.data),
        "/addtocart",
        "cart_response"
      );
      let success = false;
      let message = "Unknown response from server";
      if (response.status >= 200 && response.status < 300) {
        success = true;
        message = "Card added to cart successfully";
      } else {
        message = `Server returned status code ${response.status}`;
      }
      return {
        success,
        message,
        debugFile
      };
    } catch (error) {
      console.error("Error adding card to cart:", error);
      return {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      };
    }
  }
  /**
   * Check if the response indicates wrong credentials
   */
  checkForAuthenticationIssues(html) {
    if (html.includes("<title>Patrick Market</title>") && html.includes('form class="login100-form validate-form"')) {
      return { isAuthenticated: false, reason: "Login page detected, session expired or cookies invalid" };
    }
    if (html.includes('input class="form-control form-control-lg" name="captcha"')) {
      return { isAuthenticated: false, reason: "Captcha detected, authentication challenge present" };
    }
    return { isAuthenticated: true };
  }
  /**
   * Save search results to a JSON file
   */
  async crawl(params = {}) {
    try {
      const result = await this.searchCards(params);
      if (!result.authenticated) {
        throw new Error(`Authentication failure: ${result.reason}`);
      }
      if (IS_DEBUG) {
        await saveCardData(result.cards, this.title, result.pagination.currentPage);
      }
      return result.cards || [];
    } catch (error) {
      console.error("Error saving results:", error);
      throw error;
    }
  }
  /**
   * Process checkout after adding cards to cart
   * @returns Response data from the server
   */
  async checkout() {
    try {
      console.warn("Processing checkout...");
      const originalHeaders = this.crawler.getHeaders();
      this.crawler.setHeaders({
        ...originalHeaders,
        Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        Referer: `https://${this.baseHost}/cards/buy`
      });
      const checkoutResult = await this.crawler.executeCheckout("/checkout");
      if (IS_DEBUG) {
        console.warn(`Checkout status: ${checkoutResult.status}`);
        console.warn(`Final URL: ${checkoutResult.finalUrl}`);
        if (checkoutResult.redirectChain.length > 1) {
          console.warn(`Redirect chain: ${checkoutResult.redirectChain.join(" -> ")}`);
        }
      }
      const debugFile = await this.saveDebugHtml(
        checkoutResult.html,
        checkoutResult.finalUrl,
        "checkout_response"
      );
      this.crawler.setHeaders(originalHeaders);
      const isSuccess = checkoutResult.redirectChain.some((url) => url.includes("/cards/my"));
      return {
        success: isSuccess,
        message: isSuccess ? "Checkout successful" : "Checkout failed",
        debugFile
      };
    } catch (error) {
      console.error("Error during checkout:", error);
      return {
        success: false,
        message: `Error: ${error instanceof Error ? error.message : "Unknown error"}`
      };
    }
  }
  /**
   * Save purchased cards to the database
   */
  async savePurchasedCardsToDatabase(cards) {
    try {
      for (const card of cards) {
        await savePurchasedCard(card);
        if (IS_DEBUG) {
          console.warn(`Saved purchase of card ${card.id} to database`);
        }
      }
    } catch (error) {
      console.error("Error saving purchased cards to database:", error);
    }
  }
};

// src/message-emitter.ts
var MessageEmitter = class {
  static {
    this.isEnabled = process.env.NODE_ENV !== "test";
  }
  static info(message) {
    this.emit("info", message);
  }
  static warn(message) {
    this.emit("warn", message);
  }
  static error(message) {
    this.emit("error", message);
  }
  static success(message) {
    this.emit("success", message);
  }
  static emit(type, message) {
    if (!this.isEnabled)
      return;
    const data = {
      type,
      message,
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    };
    console.log(`__CRAWLER_MSG__${JSON.stringify(data)}`);
  }
  // Enable/disable message emission (useful for testing)
  static setEnabled(enabled) {
    this.isEnabled = enabled;
  }
};
var originalConsoleWarn = console.warn;
var originalConsoleError = console.error;
console.warn = (...args) => {
  const message = args.join(" ");
  MessageEmitter.warn(message);
  originalConsoleWarn(...args);
};
console.error = (...args) => {
  const message = args.join(" ");
  MessageEmitter.error(message);
  originalConsoleError(...args);
};

// src/proxy-manager.ts
var ProxyManager = class {
  /**
   * Create a new proxy manager with a list of proxies
   * @param proxies List of proxies in format: *****************************:port
   */
  constructor(proxies = []) {
    this.currentIndex = 0;
    this.proxies = proxies;
  }
  /**
   * Add a proxy to the rotation list
   */
  addProxy(proxy) {
    this.proxies.push(proxy);
  }
  /**
   * Add multiple proxies to the rotation list
   */
  addProxies(proxies) {
    this.proxies.push(...proxies);
  }
  /**
   * Get the next proxy in the rotation
   * @returns The next proxy URL or undefined if no proxies are available
   */
  getNextProxy() {
    if (this.proxies.length === 0) {
      return void 0;
    }
    const proxy = this.proxies[this.currentIndex];
    this.currentIndex = (this.currentIndex + 1) % this.proxies.length;
    return proxy;
  }
  /**
   * Get the current number of available proxies
   */
  get count() {
    return this.proxies.length;
  }
};

// src/index.ts
async function loadSearchParams() {
  try {
    const config2 = loadConfig();
    if (config2.searchParams) {
      if (IS_DEBUG) {
        console.warn("Loaded search parameters from config.json");
      }
      return config2.searchParams;
    }
    throw new Error("No search parameters found in config");
  } catch (error) {
    throw new Error(`Error loading config, using default parameters: ${error}`);
  }
}
async function main() {
  try {
    MessageEmitter.info("Initializing card crawler...");
    await initializeDatabase();
    MessageEmitter.success("Database initialized successfully");
    const config2 = loadConfig();
    MessageEmitter.info(`Starting card crawler for ${config2.targetHost}`);
    let proxyManager;
    if (SAMPLE_PROXIES.length > 0) {
      proxyManager = new ProxyManager(SAMPLE_PROXIES);
      MessageEmitter.info(`Configured ${proxyManager.count} proxies for rotation`);
    }
    const cardCrawler = new CardCrawler(
      config2.targetHost,
      config2.cookies,
      proxyManager,
      {
        userAgent: config2.userAgent,
        debug: true,
        debugDir: "debug_html"
      }
    );
    MessageEmitter.success("Card crawler initialized successfully");
    const searchParams = await loadSearchParams();
    MessageEmitter.info("Search parameters loaded from config");
    await excute(cardCrawler, searchParams);
  } catch (error) {
    const message = error instanceof Error ? error.message : error;
    MessageEmitter.error(`Critical error in main function: ${message}`);
    console.error("Critical error in main function:", message);
    MessageEmitter.info("Attempting to restart in 10 seconds...");
    await delay(10);
    return main();
  }
}
async function excute(cardCrawler, searchParams) {
  try {
    MessageEmitter.info("\u{1F504} Starting new crawling iteration...");
    const cards = await cardCrawler.crawl(searchParams);
    if (cards.length === 0) {
      MessageEmitter.warn("No cards found. Waiting for next run...");
      await delay(DELAY_PER_RUN);
    } else {
      const selectedCards = cards.slice(0, LIMIT_PER_RUN);
      MessageEmitter.success(`Found ${cards.length} cards, selecting ${selectedCards.length} for purchase`);
      MessageEmitter.info("Adding cards to cart...");
      const t1 = Date.now();
      const addToCartResult = await cardCrawler.addToCart(selectedCards.map((card) => card.id));
      if (addToCartResult.debugFile && IS_DEBUG) {
        MessageEmitter.info(`Cart debug file saved to: ${addToCartResult.debugFile}`);
      }
      if (!addToCartResult.success) {
        MessageEmitter.error(`Failed to add cards to cart: ${addToCartResult.message}`);
        MessageEmitter.info("Waiting before next run...");
        await delay(3);
      } else {
        MessageEmitter.success("Cards added to cart successfully");
        MessageEmitter.info("Proceeding to checkout...");
        const checkoutResult = await cardCrawler.checkout();
        if (checkoutResult.debugFile && IS_DEBUG) {
          MessageEmitter.info(`Checkout debug file saved to: ${checkoutResult.debugFile}`);
        }
        if (checkoutResult.success) {
          MessageEmitter.success(checkoutResult.message);
          MessageEmitter.success(`Successfully purchased ${selectedCards.length} cards`);
          MessageEmitter.info("Saving purchased cards to database...");
          await cardCrawler.savePurchasedCardsToDatabase(selectedCards);
          MessageEmitter.success("Cards saved to database successfully");
        } else {
          MessageEmitter.error(`Checkout failed: ${checkoutResult.message}`);
        }
        const timeTaken = Date.now() - t1;
        MessageEmitter.info(`\u2705 Iteration completed in ${timeTaken}ms`);
        MessageEmitter.info("\u23F1\uFE0F Waiting 3 seconds before next run...");
        await delay(3);
      }
    }
  } catch (error) {
    const message = error instanceof Error ? error.message : error;
    MessageEmitter.error(`\u274C Error in crawling iteration: ${message}`);
    MessageEmitter.info("\u23F1\uFE0F Waiting 5 seconds before retrying...");
    await delay(5);
  }
  MessageEmitter.success("\u{1F493} Crawler heartbeat - process is running");
  await excute(cardCrawler, searchParams);
}
async function delay(time) {
  await new Promise((resolve) => setTimeout(resolve, time * 1e3));
}
process3.on("SIGINT", () => {
  MessageEmitter.info("Received SIGINT, shutting down gracefully...");
  closeDatabase();
  process3.exit(0);
});
process3.on("SIGTERM", () => {
  MessageEmitter.info("Received SIGTERM, shutting down gracefully...");
  closeDatabase();
  process3.exit(0);
});
process3.on("uncaughtException", (error) => {
  MessageEmitter.error(`Uncaught exception: ${error.message}`);
  console.error("Uncaught exception:", error);
  closeDatabase();
  process3.exit(1);
});
process3.on("unhandledRejection", (reason, promise) => {
  MessageEmitter.error(`Unhandled rejection at: ${promise}, reason: ${reason}`);
  console.error("Unhandled rejection:", reason);
  closeDatabase();
  process3.exit(1);
});
main().catch((error) => {
  console.error("Main function failed:", error);
  closeDatabase();
  process3.exit(1);
});
